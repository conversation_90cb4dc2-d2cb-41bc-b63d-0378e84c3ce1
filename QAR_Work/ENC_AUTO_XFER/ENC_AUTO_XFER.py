import os
import shutil
import sys
import json
import time
import zipfile
from datetime import datetime
from collections import deque
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QLineEdit, QFileDialog, QWidget,
                             QProgressBar, QMessageBox, QGroupBox, QFrame, QDesktopWidget,
                             QCheckBox, QGridLayout)
from PyQt5.QtCore import QTimer, Qt, QFileSystemWatcher
from PyQt5.QtGui import QFont, QPalette, QFontDatabase, QIcon


class ENCAutoXferApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ENC_AUTO_XFER 飞机WQAR数据自动传输工具")

        # 获取屏幕信息和DPI缩放
        self.setup_display_settings()

        # 设置窗口尺寸（根据DPI自适应，调整为更大的尺寸以容纳双路径显示）
        window_width = int(680 * self.scale_factor)
        window_height = int(520 * self.scale_factor)
        self.setGeometry(100, 100, window_width, window_height)

        # 设置窗口样式
        self.setup_window_style()

        # 初始化路径变量
        self.source_path = "Z:\\DATA_BAK\\ENC_BAK"  # path1
        self.intermediate_path = "E:\\ENC"  # path2
        self.cast_source_path = os.path.join(self.intermediate_path, "ENC_CAST")
        self.comac_source_path = os.path.join(self.intermediate_path, "ENC_COMAC")
        self.target_base_path = "D:\\NON-WGL"  # 目标基础路径
        self.cast_target_path = os.path.join(self.target_base_path, "CAST")
        self.comac_target_path = os.path.join(self.target_base_path, "COMAC")
        self.ac_type_config_path = "Z:\\DATA_BAK\\config\\AC_TYPE.json"

        # 初始化变量
        self.init_variables()

        # 定时器
        self.init_timers()

        # 文件系统监控
        self.init_file_watchers()

        # 加载历史数据
        self.load_history_data()

        # 设置程序图标
        self.set_app_icon()

        # 创建UI
        self.init_ui()

        # 开始监控
        self.start_monitoring()

    def setup_display_settings(self):
        """设置显示相关参数"""
        # 获取屏幕信息
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        self.screen_width = screen_rect.width()
        self.screen_height = screen_rect.height()

        # 计算DPI缩放因子
        app = QApplication.instance()
        if app:
            screen = app.primaryScreen()
            dpi = screen.logicalDotsPerInch()
            self.scale_factor = max(1.0, dpi / 96.0)

            if self.screen_width >= 3840:  # 4K或更高分辨率
                self.scale_factor = max(self.scale_factor, 1.5)
            elif self.screen_width >= 2560:  # 2K分辨率
                self.scale_factor = max(self.scale_factor, 1.25)
        else:
            self.scale_factor = 1.0

        # 计算基础字体大小
        self.base_font_size = max(10, int(11 * self.scale_factor))
        self.large_font_size = max(11, int(13 * self.scale_factor))
        self.huge_font_size = max(36, int(44 * self.scale_factor))

        print(f"屏幕分辨率: {self.screen_width}x{self.screen_height}")
        print(f"DPI缩放因子: {self.scale_factor:.2f}")

    def setup_window_style(self):
        """设置窗口样式"""
        padding_size = max(8, int(10 * self.scale_factor))
        border_radius = max(4, int(6 * self.scale_factor))
        margin_size = max(10, int(12 * self.scale_factor))

        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #f5f5f5;
            }}
            QGroupBox {{
                font-weight: bold;
                border: {max(1, int(2 * self.scale_factor))}px solid #cccccc;
                border-radius: {border_radius}px;
                margin-top: 1ex;
                padding-top: {margin_size}px;
                background-color: white;
                font-size: {self.base_font_size}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {margin_size}px;
                padding: 0 {padding_size}px 0 {padding_size}px;
                color: #2c3e50;
                font-size: {self.large_font_size}px;
                font-weight: bold;
            }}
            QLabel {{
                color: #2c3e50;
                font-size: {self.base_font_size}px;
            }}
            QLineEdit {{
                border: 1px solid #bdc3c7;
                border-radius: {max(3, int(4 * self.scale_factor))}px;
                padding: {padding_size}px;
                font-size: {self.base_font_size}px;
                background-color: white;
                min-height: {max(20, int(25 * self.scale_factor))}px;
            }}
            QLineEdit:focus {{
                border-color: #3498db;
                border-width: {max(1, int(2 * self.scale_factor))}px;
            }}
            QPushButton {{
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: {border_radius}px;
                padding: {padding_size}px {int(padding_size * 1.5)}px;
                font-size: {self.base_font_size}px;
                font-weight: bold;
                min-height: {max(25, int(30 * self.scale_factor))}px;
            }}
            QPushButton:hover {{
                background-color: #2980b9;
            }}
            QPushButton:pressed {{
                background-color: #21618c;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
            QCheckBox {{
                font-size: {self.base_font_size}px;
                color: #2c3e50;
                font-weight: bold;
            }}
            QCheckBox::indicator {{
                width: {max(16, int(18 * self.scale_factor))}px;
                height: {max(16, int(18 * self.scale_factor))}px;
            }}
        """)

    def init_variables(self):
        """初始化变量"""
        # 文件队列
        self.folder_queue = deque()  # 待压缩文件夹队列
        self.cast_file_queue = deque()  # CAST待复制文件队列
        self.comac_file_queue = deque()  # COMAC待复制文件队列
        
        # 统计变量
        self.enc_files_count = 0  # ENC文件数
        self.cast_copied_count = 0  # CAST已复制数
        self.comac_copied_count = 0  # COMAC已复制数
        self.total_runtime = 0  # 总运行时长（秒）
        self.cast_copy_time = 0  # CAST复制时长
        self.comac_copy_time = 0  # COMAC复制时长
        
        # 状态变量
        self.is_running = False
        self.is_paused = False
        self.cast_enabled = True  # CAST复制开关
        self.comac_enabled = True  # COMAC复制开关
        self.start_time = None
        self.last_exit_time = None  # 上次程序退出时间
        
        # 等待时间变量
        self.cast_waiting_seconds = 0
        self.comac_waiting_seconds = 0
        self.cast_total_waiting_seconds = 0  # CAST总等待时间
        self.comac_total_waiting_seconds = 0  # COMAC总等待时间
        self.cast_average_wait_time = 0
        self.comac_average_wait_time = 0
        
        # 文件完整性检查
        self.pending_folders = {}  # 待检查文件夹
        self.pending_cast_files = {}  # 待检查CAST文件
        self.pending_comac_files = {}  # 待检查COMAC文件

    def init_timers(self):
        """初始化定时器"""
        # 文件夹监控定时器
        self.folder_monitor_timer = QTimer()
        self.folder_monitor_timer.timeout.connect(self.monitor_source_folder)
        
        # 文件完整性检查定时器
        self.folder_check_timer = QTimer()
        self.folder_check_timer.timeout.connect(self.check_folder_integrity)
        
        self.cast_file_check_timer = QTimer()
        self.cast_file_check_timer.timeout.connect(self.check_cast_file_integrity)
        
        self.comac_file_check_timer = QTimer()
        self.comac_file_check_timer.timeout.connect(self.check_comac_file_integrity)
        
        # CAST复制流程定时器
        self.cast_countdown_timer = QTimer()
        self.cast_countdown_timer.timeout.connect(self.update_cast_countdown)
        
        self.cast_folder_check_timer = QTimer()
        self.cast_folder_check_timer.timeout.connect(self.check_cast_target_folder)
        
        # COMAC复制流程定时器
        self.comac_countdown_timer = QTimer()
        self.comac_countdown_timer.timeout.connect(self.update_comac_countdown)
        
        self.comac_folder_check_timer = QTimer()
        self.comac_folder_check_timer.timeout.connect(self.check_comac_target_folder)
        
        # 运行时间更新定时器
        self.runtime_update_timer = QTimer()
        self.runtime_update_timer.timeout.connect(self.update_runtime_display)
        self.runtime_update_timer.start(1000)

    def init_file_watchers(self):
        """初始化文件系统监控"""
        self.source_watcher = QFileSystemWatcher()
        self.source_watcher.directoryChanged.connect(self.on_source_directory_changed)
        
        self.cast_watcher = QFileSystemWatcher()
        self.cast_watcher.directoryChanged.connect(self.on_cast_directory_changed)
        
        self.comac_watcher = QFileSystemWatcher()
        self.comac_watcher.directoryChanged.connect(self.on_comac_directory_changed)

    def get_program_directory(self):
        """获取程序所在目录"""
        if getattr(sys, 'frozen', False):
            return os.path.dirname(sys.executable)
        else:
            return os.path.dirname(os.path.abspath(__file__))

    def set_app_icon(self):
        """设置程序图标"""
        try:
            program_dir = self.get_program_directory()
            icon_path = os.path.join(program_dir, "app.ico")
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
                QApplication.instance().setWindowIcon(icon)
                print(f"设置程序图标: {icon_path}")
        except Exception as e:
            print(f"设置图标失败: {str(e)}")

    def init_ui(self):
        """初始化UI界面"""
        main_widget = QWidget()
        main_layout = QVBoxLayout()

        # DPI自适应的间距和边距
        spacing = max(8, int(10 * self.scale_factor))
        margin = max(12, int(15 * self.scale_factor))

        main_layout.setSpacing(spacing)
        main_layout.setContentsMargins(margin, margin, margin, margin)

        # 路径设置分组
        path_group = self.create_path_group()
        main_layout.addWidget(path_group)

        # 复制状态分组（双路径显示）
        status_group = self.create_status_group()
        main_layout.addWidget(status_group)

        # 等待时间显示分组
        countdown_group = self.create_countdown_group()
        main_layout.addWidget(countdown_group)

        # 控制按钮分组
        control_group = self.create_control_group()
        main_layout.addWidget(control_group)

        main_layout.addStretch()
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

    def create_path_group(self):
        """创建路径设置分组"""
        path_group = QGroupBox("路径设置")
        path_layout = QVBoxLayout()
        path_layout.setSpacing(max(8, int(10 * self.scale_factor)))

        # DPI自适应的控件尺寸
        label_width = max(80, int(90 * self.scale_factor))
        button_width = max(60, int(70 * self.scale_factor))
        input_height = max(25, int(30 * self.scale_factor))
        button_height = max(25, int(30 * self.scale_factor))

        # 源路径设置（文件夹监控）
        source_layout = QHBoxLayout()
        source_label = QLabel("源文件夹:")
        source_label.setMinimumWidth(label_width)
        source_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.source_edit = QLineEdit(self.source_path)
        self.source_edit.setReadOnly(True)
        self.source_edit.setMinimumHeight(input_height)
        source_button = QPushButton("浏览...")
        source_button.setMinimumWidth(button_width)
        source_button.setMinimumHeight(button_height)
        source_button.clicked.connect(self.select_source_folder)
        source_layout.addWidget(source_label)
        source_layout.addWidget(self.source_edit, 1)
        source_layout.addWidget(source_button)

        # 目标路径设置
        target_layout = QHBoxLayout()
        target_label = QLabel("目标路径:")
        target_label.setMinimumWidth(label_width)
        target_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.target_base_edit = QLineEdit(self.target_base_path)
        self.target_base_edit.setReadOnly(True)
        self.target_base_edit.setMinimumHeight(input_height)
        target_button = QPushButton("浏览...")
        target_button.setMinimumWidth(button_width)
        target_button.setMinimumHeight(button_height)
        target_button.clicked.connect(self.select_target_base_folder)
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_base_edit, 1)
        target_layout.addWidget(target_button)

        path_layout.addLayout(source_layout)
        path_layout.addLayout(target_layout)
        path_group.setLayout(path_layout)
        return path_group

    def create_status_group(self):
        """创建状态显示分组（双路径显示）"""
        status_group = QGroupBox("复制状态")
        status_layout = QVBoxLayout()
        status_layout.setSpacing(10)

        # ENC文件统计
        self.enc_stats_label = QLabel("ENC文件数: 0")
        self.enc_stats_label.setAlignment(Qt.AlignCenter)
        info_padding = max(6, int(8 * self.scale_factor))
        info_radius = max(3, int(4 * self.scale_factor))
        self.enc_stats_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #34495e;
                padding: {info_padding}px;
                background-color: #ecf0f1;
                border-radius: {info_radius}px;
            }}
        """)

        # 双路径状态显示
        dual_status_layout = QHBoxLayout()

        # CAST状态
        cast_status_layout = QVBoxLayout()
        cast_title = QLabel("CAST")
        cast_title.setAlignment(Qt.AlignCenter)
        cast_title.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #e74c3c;
                padding: 4px;
            }}
        """)
        self.cast_stats_label = QLabel("已复制: 0 | 剩余: 0")
        self.cast_stats_label.setAlignment(Qt.AlignCenter)
        self.cast_stats_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #2c3e50;
                padding: {info_padding}px;
                background-color: #fdf2f2;
                border-radius: {info_radius}px;
                border: 1px solid #fadbd8;
            }}
        """)
        cast_status_layout.addWidget(cast_title)
        cast_status_layout.addWidget(self.cast_stats_label)

        # COMAC状态
        comac_status_layout = QVBoxLayout()
        comac_title = QLabel("COMAC")
        comac_title.setAlignment(Qt.AlignCenter)
        comac_title.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #3498db;
                padding: 4px;
            }}
        """)
        self.comac_stats_label = QLabel("已复制: 0 | 剩余: 0")
        self.comac_stats_label.setAlignment(Qt.AlignCenter)
        self.comac_stats_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #2c3e50;
                padding: {info_padding}px;
                background-color: #f0f8ff;
                border-radius: {info_radius}px;
                border: 1px solid #bde5f8;
            }}
        """)
        comac_status_layout.addWidget(comac_title)
        comac_status_layout.addWidget(self.comac_stats_label)

        dual_status_layout.addLayout(cast_status_layout)
        dual_status_layout.addLayout(comac_status_layout)

        status_layout.addWidget(self.enc_stats_label)
        status_layout.addLayout(dual_status_layout)
        status_group.setLayout(status_layout)
        return status_group

    def create_countdown_group(self):
        """创建等待时间显示分组"""
        countdown_group = QGroupBox("等待时间")
        countdown_layout = QVBoxLayout()
        countdown_layout.setAlignment(Qt.AlignCenter)

        # 四栏布局：总运行时长、CAST等待时间、COMAC等待时间、平均等待时间
        wait_time_layout = QHBoxLayout()

        # 总运行时长
        total_runtime_layout = QVBoxLayout()
        total_runtime_layout.setAlignment(Qt.AlignCenter)
        total_runtime_title = QLabel("总运行时长")
        total_runtime_title.setAlignment(Qt.AlignCenter)
        total_runtime_title.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                font-weight: bold;
            }}
        """)

        self.total_runtime_label = QLabel("0天00:00:00")
        self.total_runtime_label.setAlignment(Qt.AlignCenter)
        runtime_font = QFont()
        runtime_font.setPointSize(int(self.huge_font_size * 0.5))
        runtime_font.setBold(True)
        self.total_runtime_label.setFont(runtime_font)

        countdown_padding = max(6, int(8 * self.scale_factor))
        countdown_margin = max(3, int(4 * self.scale_factor))
        countdown_radius = max(4, int(6 * self.scale_factor))
        countdown_border = max(1, int(2 * self.scale_factor))

        self.total_runtime_label.setStyleSheet(f"""
            QLabel {{
                color: #27ae60;
                background-color: #f0fff4;
                border: {countdown_border}px solid #c3e6cb;
                border-radius: {countdown_radius}px;
                padding: {countdown_padding}px;
                margin: {countdown_margin}px;
                font-size: {int(self.huge_font_size * 0.5)}px;
                font-weight: bold;
            }}
        """)

        total_runtime_layout.addWidget(total_runtime_title)
        total_runtime_layout.addWidget(self.total_runtime_label)

        # CAST当前等待时间
        cast_wait_layout = QVBoxLayout()
        cast_wait_layout.setAlignment(Qt.AlignCenter)
        cast_wait_title = QLabel("CAST等待时间")
        cast_wait_title.setAlignment(Qt.AlignCenter)
        cast_wait_title.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                font-weight: bold;
            }}
        """)

        self.cast_countdown_label = QLabel("00:00")
        self.cast_countdown_label.setAlignment(Qt.AlignCenter)
        countdown_font = QFont()
        countdown_font.setPointSize(int(self.huge_font_size * 0.6))
        countdown_font.setBold(True)
        self.cast_countdown_label.setFont(countdown_font)

        self.cast_countdown_label.setStyleSheet(f"""
            QLabel {{
                color: #e74c3c;
                background-color: #fdf2f2;
                border: {countdown_border}px solid #fadbd8;
                border-radius: {countdown_radius}px;
                padding: {countdown_padding}px;
                margin: {countdown_margin}px;
                font-size: {int(self.huge_font_size * 0.6)}px;
                font-weight: bold;
            }}
        """)

        cast_wait_layout.addWidget(cast_wait_title)
        cast_wait_layout.addWidget(self.cast_countdown_label)

        # COMAC当前等待时间
        comac_wait_layout = QVBoxLayout()
        comac_wait_layout.setAlignment(Qt.AlignCenter)
        comac_wait_title = QLabel("COMAC等待时间")
        comac_wait_title.setAlignment(Qt.AlignCenter)
        comac_wait_title.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                font-weight: bold;
            }}
        """)

        self.comac_countdown_label = QLabel("00:00")
        self.comac_countdown_label.setAlignment(Qt.AlignCenter)
        self.comac_countdown_label.setFont(countdown_font)
        self.comac_countdown_label.setStyleSheet(f"""
            QLabel {{
                color: #3498db;
                background-color: #f0f8ff;
                border: {countdown_border}px solid #bde5f8;
                border-radius: {countdown_radius}px;
                padding: {countdown_padding}px;
                margin: {countdown_margin}px;
                font-size: {int(self.huge_font_size * 0.6)}px;
                font-weight: bold;
            }}
        """)

        comac_wait_layout.addWidget(comac_wait_title)
        comac_wait_layout.addWidget(self.comac_countdown_label)

        # 平均等待时间
        avg_wait_layout = QVBoxLayout()
        avg_wait_layout.setAlignment(Qt.AlignCenter)
        avg_wait_title = QLabel("平均等待时间")
        avg_wait_title.setAlignment(Qt.AlignCenter)
        avg_wait_title.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                font-weight: bold;
            }}
        """)

        self.avg_wait_label = QLabel("00:00")
        self.avg_wait_label.setAlignment(Qt.AlignCenter)
        self.avg_wait_label.setFont(countdown_font)
        self.avg_wait_label.setStyleSheet(f"""
            QLabel {{
                color: #9b59b6;
                background-color: #faf5ff;
                border: {countdown_border}px solid #e1bee7;
                border-radius: {countdown_radius}px;
                padding: {countdown_padding}px;
                margin: {countdown_margin}px;
                font-size: {int(self.huge_font_size * 0.6)}px;
                font-weight: bold;
            }}
        """)

        avg_wait_layout.addWidget(avg_wait_title)
        avg_wait_layout.addWidget(self.avg_wait_label)

        wait_time_layout.addLayout(total_runtime_layout)
        wait_time_layout.addLayout(cast_wait_layout)
        wait_time_layout.addLayout(comac_wait_layout)
        wait_time_layout.addLayout(avg_wait_layout)
        countdown_layout.addLayout(wait_time_layout)
        countdown_group.setLayout(countdown_layout)
        return countdown_group

    def create_control_group(self):
        """创建控制按钮分组"""
        control_group = QGroupBox("操作控制")
        control_layout = QVBoxLayout()
        control_layout.setSpacing(max(8, int(10 * self.scale_factor)))

        # 复制选择框
        checkbox_layout = QHBoxLayout()
        checkbox_layout.setAlignment(Qt.AlignCenter)

        self.cast_checkbox = QCheckBox("CAST")
        self.cast_checkbox.setChecked(True)
        self.cast_checkbox.stateChanged.connect(self.on_cast_checkbox_changed)

        self.comac_checkbox = QCheckBox("COMAC")
        self.comac_checkbox.setChecked(True)
        self.comac_checkbox.stateChanged.connect(self.on_comac_checkbox_changed)

        checkbox_layout.addWidget(self.cast_checkbox)
        checkbox_layout.addWidget(self.comac_checkbox)

        # 控制按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(max(8, int(10 * self.scale_factor)))

        # DPI自适应的按钮尺寸
        button_height = max(32, int(38 * self.scale_factor))
        button_width = max(80, int(95 * self.scale_factor))
        button_font_size = max(10, int(12 * self.scale_factor))

        self.start_button = QPushButton("🚀 开始监控")
        self.start_button.setMinimumHeight(button_height)
        self.start_button.setMinimumWidth(button_width)
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #27ae60;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #229954;
            }}
            QPushButton:pressed {{
                background-color: #1e8449;
            }}
        """)
        self.start_button.clicked.connect(self.start_monitoring_process)

        self.pause_button = QPushButton("⏸ 暂停监控")
        self.pause_button.setMinimumHeight(button_height)
        self.pause_button.setMinimumWidth(button_width)
        self.pause_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #f39c12;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #e67e22;
            }}
            QPushButton:pressed {{
                background-color: #d35400;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """)
        self.pause_button.clicked.connect(self.pause_monitoring_process)
        self.pause_button.setEnabled(False)

        self.exit_button = QPushButton("❌ 退出程序")
        self.exit_button.setMinimumHeight(button_height)
        self.exit_button.setMinimumWidth(button_width)
        self.exit_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #e74c3c;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #c0392b;
            }}
            QPushButton:pressed {{
                background-color: #a93226;
            }}
        """)
        self.exit_button.clicked.connect(self.exit_program)

        button_layout.addStretch()
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.pause_button)
        button_layout.addWidget(self.exit_button)
        button_layout.addStretch()

        control_layout.addLayout(checkbox_layout)
        control_layout.addLayout(button_layout)
        control_group.setLayout(control_layout)
        return control_group

    def load_history_data(self):
        """加载历史数据"""
        try:
            program_dir = self.get_program_directory()
            history_file = os.path.join(program_dir, "ENC.json")

            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.enc_files_count = data.get('enc_files_count', 0)
                    self.cast_copied_count = data.get('cast_copied_count', 0)
                    self.comac_copied_count = data.get('comac_copied_count', 0)
                    self.total_runtime = data.get('total_runtime', 0)
                    self.cast_copy_time = data.get('cast_copy_time', 0)
                    self.comac_copy_time = data.get('comac_copy_time', 0)
                    self.cast_enabled = data.get('cast_enabled', True)
                    self.comac_enabled = data.get('comac_enabled', True)
                    self.cast_total_waiting_seconds = data.get('cast_total_waiting_seconds', 0)
                    self.comac_total_waiting_seconds = data.get('comac_total_waiting_seconds', 0)
                    # 加载上次退出时间
                    last_exit_str = data.get('last_exit_time', None)
                    if last_exit_str:
                        try:
                            self.last_exit_time = datetime.fromisoformat(last_exit_str)
                            print(f"上次程序退出时间: {self.last_exit_time.strftime('%Y-%m-%d %H:%M:%S')}")
                        except Exception as e:
                            print(f"解析退出时间失败: {str(e)}")
                            self.last_exit_time = None
                    print(f"加载历史数据: ENC文件数={self.enc_files_count}, CAST已复制={self.cast_copied_count}, COMAC已复制={self.comac_copied_count}")
            else:
                print(f"历史数据文件不存在，创建新文件: {history_file}")
                self.create_initial_history_file()

        except Exception as e:
            print(f"加载历史数据失败: {str(e)}")
            self.create_initial_history_file()

    def create_initial_history_file(self):
        """创建初始历史数据文件"""
        try:
            program_dir = self.get_program_directory()
            history_file = os.path.join(program_dir, "ENC.json")

            initial_data = {
                'enc_files_count': 0,
                'cast_copied_count': 0,
                'comac_copied_count': 0,
                'total_runtime': 0,
                'cast_copy_time': 0,
                'comac_copy_time': 0,
                'cast_enabled': True,
                'comac_enabled': True,
                'created_time': datetime.now().isoformat(),
                'last_update': datetime.now().isoformat()
            }

            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(initial_data, f, ensure_ascii=False, indent=2)

            print(f"成功创建初始历史数据文件: {history_file}")

        except Exception as e:
            print(f"创建初始历史数据文件失败: {str(e)}")

    def save_history_data(self):
        """保存历史数据"""
        try:
            program_dir = self.get_program_directory()
            history_file = os.path.join(program_dir, "ENC.json")

            data = {
                'enc_files_count': self.enc_files_count,
                'cast_copied_count': self.cast_copied_count,
                'comac_copied_count': self.comac_copied_count,
                'total_runtime': self.total_runtime,
                'cast_copy_time': self.cast_copy_time,
                'comac_copy_time': self.comac_copy_time,
                'cast_enabled': self.cast_enabled,
                'comac_enabled': self.comac_enabled,
                'cast_total_waiting_seconds': self.cast_total_waiting_seconds,
                'comac_total_waiting_seconds': self.comac_total_waiting_seconds,
                'last_exit_time': datetime.now().isoformat(),
                'last_update': datetime.now().isoformat()
            }

            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"保存历史数据到: {history_file}")
        except Exception as e:
            print(f"保存历史数据失败: {str(e)}")

    def load_aircraft_type_config(self):
        """加载飞机型号配置"""
        try:
            if os.path.exists(self.ac_type_config_path):
                # 尝试使用utf-8-sig编码处理BOM
                try:
                    with open(self.ac_type_config_path, 'r', encoding='utf-8-sig') as f:
                        return json.load(f)
                except UnicodeDecodeError:
                    # 如果utf-8-sig失败，尝试utf-8
                    with open(self.ac_type_config_path, 'r', encoding='utf-8') as f:
                        return json.load(f)
            else:
                print(f"飞机型号配置文件不存在: {self.ac_type_config_path}")
                return {}
        except Exception as e:
            print(f"加载飞机型号配置失败: {str(e)}")
            return {}

    def get_aircraft_type(self, folder_name):
        """根据文件夹名获取飞机型号"""
        if len(folder_name) < 6:
            return None

        aircraft_id = folder_name[:6]
        ac_type_config = self.load_aircraft_type_config()
        return ac_type_config.get(aircraft_id, None)

    def is_arj21_aircraft(self, folder_name):
        """判断是否为ARJ21机型"""
        aircraft_type = self.get_aircraft_type(folder_name)
        return aircraft_type == "ARJ21"

    def start_monitoring(self):
        """开始监控"""
        # 确保必要的目录存在
        self.ensure_directories_exist()

        # 开始监控源文件夹
        if os.path.exists(self.source_path):
            self.source_watcher.addPath(self.source_path)
            print(f"开始监控源文件夹: {self.source_path}")

        # 开始监控中间路径文件夹
        if os.path.exists(self.cast_source_path):
            self.cast_watcher.addPath(self.cast_source_path)
            print(f"开始监控CAST源文件夹: {self.cast_source_path}")

        if os.path.exists(self.comac_source_path):
            self.comac_watcher.addPath(self.comac_source_path)
            print(f"开始监控COMAC源文件夹: {self.comac_source_path}")

        # 启动定时监控
        self.folder_monitor_timer.start(5000)  # 每5秒检查一次
        self.folder_check_timer.start(500)     # 每0.5秒检查文件夹完整性
        self.cast_file_check_timer.start(500)  # 每0.5秒检查CAST文件完整性
        self.comac_file_check_timer.start(500) # 每0.5秒检查COMAC文件完整性

        # 初始扫描
        self.scan_source_folder()
        self.scan_cast_source_folder()
        self.scan_comac_source_folder()

        # 更新状态显示
        self.update_status_display()

    def ensure_directories_exist(self):
        """确保必要的目录存在"""
        directories = [
            self.source_path,
            self.intermediate_path,
            self.cast_source_path,
            self.comac_source_path,
            self.target_base_path,
            self.cast_target_path,
            self.comac_target_path
        ]

        for directory in directories:
            if not os.path.exists(directory):
                try:
                    os.makedirs(directory)
                    print(f"创建目录: {directory}")
                except Exception as e:
                    print(f"创建目录失败 {directory}: {str(e)}")

    def format_time_duration(self, seconds):
        """格式化时间长度为x天hh:mm:ss格式"""
        if seconds < 0:
            return "0天00:00:00"

        days = int(seconds // 86400)
        hours = int((seconds % 86400) // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        return f"{days}天{hours:02d}:{minutes:02d}:{secs:02d}"

    def format_time_hhmm(self, seconds):
        """格式化时间为HH:MM格式"""
        if seconds < 0:
            return "--:--"
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"

    def scan_source_folder(self):
        """扫描源文件夹，将新文件夹加入队列"""
        if not os.path.exists(self.source_path):
            return

        try:
            # 获取所有文件夹
            for item in os.listdir(self.source_path):
                item_path = os.path.join(self.source_path, item)
                if os.path.isdir(item_path):
                    # 检查是否已在队列中
                    if item_path in [folder_info[0] for folder_info in self.folder_queue]:
                        continue

                    # 检查是否已在待检查列表中
                    if item_path in self.pending_folders:
                        continue

                    # 检查文件夹时间是否比上次退出时间新
                    if self.last_exit_time and not self.is_folder_newer_than_exit_time(item_path):
                        print(f"跳过旧文件夹（早于上次退出时间）: {item}")
                        continue

                    # 新文件夹，加入待检查列表
                    self.pending_folders[item_path] = {
                        'start_time': time.time(),
                        'check_count': 0,
                        'last_check_time': time.time()
                    }
                    print(f"发现新文件夹，开始完整性检查: {item}")

        except Exception as e:
            print(f"扫描源文件夹出错: {str(e)}")

    def is_folder_newer_than_exit_time(self, folder_path):
        """检查文件夹是否比上次退出时间新"""
        try:
            # 获取文件夹的修改时间
            folder_mtime = os.path.getmtime(folder_path)
            folder_datetime = datetime.fromtimestamp(folder_mtime)

            # 比较时间
            is_newer = folder_datetime > self.last_exit_time
            if not is_newer:
                print(f"文件夹 {os.path.basename(folder_path)} 时间: {folder_datetime.strftime('%Y-%m-%d %H:%M:%S')}, 上次退出: {self.last_exit_time.strftime('%Y-%m-%d %H:%M:%S')}")

            return is_newer
        except Exception as e:
            print(f"检查文件夹时间出错: {str(e)}")
            return True  # 出错时默认处理

    def check_folder_integrity(self):
        """检查文件夹完整性 - 延迟5秒后检查写入是否完成"""
        completed_folders = []

        for folder_path, folder_info in list(self.pending_folders.items()):
            if not os.path.exists(folder_path):
                completed_folders.append(folder_path)
                print(f"文件夹已不存在，移除检查: {os.path.basename(folder_path)}")
                continue

            try:
                current_time = time.time()
                # 延迟5秒后开始检查
                if current_time - folder_info['start_time'] < 5:
                    continue

                # 检查文件夹内容是否稳定（连续3次检查无变化）
                folder_files = []
                try:
                    for root, dirs, files in os.walk(folder_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            stat = os.stat(file_path)
                            folder_files.append((file_path, stat.st_size, stat.st_mtime))
                except Exception as e:
                    print(f"检查文件夹内容出错: {str(e)}")
                    continue

                # 计算文件夹内容的哈希值
                folder_hash = hash(str(sorted(folder_files)))

                if 'last_hash' not in folder_info:
                    folder_info['last_hash'] = folder_hash
                    folder_info['check_count'] = 0
                elif folder_info['last_hash'] == folder_hash:
                    folder_info['check_count'] += 1
                    if folder_info['check_count'] >= 3:
                        # 文件夹内容稳定，可以压缩
                        folder_name = os.path.basename(folder_path)
                        self.folder_queue.append((folder_path, folder_name))
                        completed_folders.append(folder_path)
                        print(f"文件夹写入完成，加入压缩队列: {folder_name}")

                        # 如果正在运行且没有暂停，开始压缩流程
                        if self.is_running and not self.is_paused:
                            QTimer.singleShot(1000, self.process_folder_queue)
                else:
                    # 文件夹内容有变化，重置检查
                    folder_info['last_hash'] = folder_hash
                    folder_info['check_count'] = 0

            except Exception as e:
                print(f"检查文件夹完整性出错: {os.path.basename(folder_path)}, 错误: {str(e)}")
                completed_folders.append(folder_path)

        # 移除已处理的文件夹
        for folder_path in completed_folders:
            self.pending_folders.pop(folder_path, None)

    def process_folder_queue(self):
        """处理文件夹队列，进行压缩"""
        if len(self.folder_queue) == 0 or not self.is_running or self.is_paused:
            return

        try:
            folder_path, folder_name = self.folder_queue.popleft()

            # 在压缩过程中检查状态
            if not self.is_running or self.is_paused:
                print(f"程序已暂停，停止压缩: {folder_name}")
                return

            if not os.path.exists(folder_path):
                print(f"源文件夹不存在，跳过: {folder_name}")
                return

            # 创建压缩文件名
            zip_filename = f"{folder_name}M.zip"
            zip_path = os.path.join(self.cast_source_path, zip_filename)

            print(f"开始压缩文件夹: {folder_name} -> {zip_filename}")

            # 压缩文件夹内容（不包含文件夹本身）
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(folder_path):
                    # 在压缩过程中定期检查状态
                    if not self.is_running or self.is_paused:
                        print(f"压缩过程中程序暂停，停止压缩: {folder_name}")
                        # 删除未完成的压缩文件
                        try:
                            if os.path.exists(zip_path):
                                os.remove(zip_path)
                        except:
                            pass
                        return

                    for file in files:
                        # 每个文件压缩前检查状态
                        if not self.is_running or self.is_paused:
                            print(f"压缩过程中程序暂停，停止压缩: {folder_name}")
                            return

                        file_path = os.path.join(root, file)
                        # 计算相对路径（相对于源文件夹）
                        arcname = os.path.relpath(file_path, folder_path)
                        zipf.write(file_path, arcname)

            # 压缩完成后再次检查状态
            if not self.is_running or self.is_paused:
                print(f"压缩完成后程序暂停，清理文件: {folder_name}")
                try:
                    if os.path.exists(zip_path):
                        os.remove(zip_path)
                except:
                    pass
                return

            print(f"压缩完成: {zip_filename}")

            # 判断是否为ARJ21机型
            if self.is_arj21_aircraft(folder_name):
                # 同时复制到COMAC文件夹
                comac_zip_path = os.path.join(self.comac_source_path, zip_filename)
                shutil.copy2(zip_path, comac_zip_path)
                print(f"ARJ21机型，同时复制到COMAC: {zip_filename}")

            # 更新ENC文件数
            self.enc_files_count += 1

            # 重要：不删除原文件夹，只生成压缩文件
            print(f"压缩完成，保留原文件夹: {folder_name}")

            # 保存历史数据
            self.save_history_data()

            # 更新状态显示
            self.update_status_display()

            # 继续处理队列中的下一个文件夹
            if len(self.folder_queue) > 0 and self.is_running and not self.is_paused:
                QTimer.singleShot(2000, self.process_folder_queue)

        except Exception as e:
            print(f"压缩文件夹时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def scan_cast_source_folder(self):
        """扫描CAST源文件夹，将新文件加入队列"""
        if not os.path.exists(self.cast_source_path):
            return

        try:
            for f in os.listdir(self.cast_source_path):
                if f.lower().endswith('.zip') and not f.startswith('~$'):
                    file_path = os.path.join(self.cast_source_path, f)

                    # 检查是否已在队列中
                    if file_path in [item[0] for item in self.cast_file_queue]:
                        continue

                    # 检查是否已在待检查列表中
                    if file_path in self.pending_cast_files:
                        continue

                    # 新文件，加入待检查列表
                    try:
                        file_stat = os.stat(file_path)
                        self.pending_cast_files[file_path] = {
                            'size': file_stat.st_size,
                            'mtime': file_stat.st_mtime,
                            'check_count': 0,
                            'start_time': time.time()
                        }
                        print(f"发现CAST文件，开始完整性检查: {f}")
                    except Exception as e:
                        print(f"获取CAST文件信息失败: {f}, 错误: {str(e)}")

        except Exception as e:
            print(f"扫描CAST源文件夹出错: {str(e)}")

    def scan_comac_source_folder(self):
        """扫描COMAC源文件夹，将新文件加入队列"""
        if not os.path.exists(self.comac_source_path):
            return

        try:
            for f in os.listdir(self.comac_source_path):
                if f.lower().endswith('.zip') and not f.startswith('~$'):
                    file_path = os.path.join(self.comac_source_path, f)

                    # 检查是否已在队列中
                    if file_path in [item[0] for item in self.comac_file_queue]:
                        continue

                    # 检查是否已在待检查列表中
                    if file_path in self.pending_comac_files:
                        continue

                    # 新文件，加入待检查列表
                    try:
                        file_stat = os.stat(file_path)
                        self.pending_comac_files[file_path] = {
                            'size': file_stat.st_size,
                            'mtime': file_stat.st_mtime,
                            'check_count': 0,
                            'start_time': time.time()
                        }
                        print(f"发现COMAC文件，开始完整性检查: {f}")
                    except Exception as e:
                        print(f"获取COMAC文件信息失败: {f}, 错误: {str(e)}")

        except Exception as e:
            print(f"扫描COMAC源文件夹出错: {str(e)}")

    def check_cast_file_integrity(self):
        """检查CAST文件完整性"""
        completed_files = []

        for file_path, file_info in list(self.pending_cast_files.items()):
            if not os.path.exists(file_path):
                completed_files.append(file_path)
                print(f"CAST文件已不存在，移除检查: {os.path.basename(file_path)}")
                continue

            try:
                file_stat = os.stat(file_path)
                current_size = file_stat.st_size
                current_mtime = file_stat.st_mtime

                if (current_size == file_info['size'] and
                    current_mtime == file_info['mtime'] and
                    current_size > 0):
                    file_info['check_count'] += 1

                    if file_info['check_count'] >= 5:
                        file_name = os.path.basename(file_path)
                        self.cast_file_queue.append((file_path, file_name, current_size))
                        completed_files.append(file_path)
                        print(f"CAST文件写入完成，加入复制队列: {file_name}")
                        self.update_status_display()

                        if (self.is_running and not self.is_paused and self.cast_enabled and
                            not self.cast_folder_check_timer.isActive() and
                            not self.cast_countdown_timer.isActive()):
                            print("新CAST文件加入队列，启动复制流程")
                            QTimer.singleShot(1000, self.try_start_cast_copy_process)
                else:
                    file_info['size'] = current_size
                    file_info['mtime'] = current_mtime
                    file_info['check_count'] = 0

            except Exception as e:
                print(f"检查CAST文件完整性出错: {os.path.basename(file_path)}, 错误: {str(e)}")
                completed_files.append(file_path)

        for file_path in completed_files:
            self.pending_cast_files.pop(file_path, None)

    def check_comac_file_integrity(self):
        """检查COMAC文件完整性"""
        completed_files = []

        for file_path, file_info in list(self.pending_comac_files.items()):
            if not os.path.exists(file_path):
                completed_files.append(file_path)
                print(f"COMAC文件已不存在，移除检查: {os.path.basename(file_path)}")
                continue

            try:
                file_stat = os.stat(file_path)
                current_size = file_stat.st_size
                current_mtime = file_stat.st_mtime

                if (current_size == file_info['size'] and
                    current_mtime == file_info['mtime'] and
                    current_size > 0):
                    file_info['check_count'] += 1

                    if file_info['check_count'] >= 5:
                        file_name = os.path.basename(file_path)
                        self.comac_file_queue.append((file_path, file_name, current_size))
                        completed_files.append(file_path)
                        print(f"COMAC文件写入完成，加入复制队列: {file_name}")
                        self.update_status_display()

                        if (self.is_running and not self.is_paused and self.comac_enabled and
                            not self.comac_folder_check_timer.isActive() and
                            not self.comac_countdown_timer.isActive()):
                            print("新COMAC文件加入队列，启动复制流程")
                            QTimer.singleShot(1000, self.try_start_comac_copy_process)
                else:
                    file_info['size'] = current_size
                    file_info['mtime'] = current_mtime
                    file_info['check_count'] = 0

            except Exception as e:
                print(f"检查COMAC文件完整性出错: {os.path.basename(file_path)}, 错误: {str(e)}")
                completed_files.append(file_path)

        for file_path in completed_files:
            self.pending_comac_files.pop(file_path, None)

    def try_start_cast_copy_process(self):
        """尝试开始CAST复制流程"""
        if (not self.is_running or self.is_paused or not self.cast_enabled or
            len(self.cast_file_queue) == 0):
            return

        if not self.cast_folder_check_timer.isActive() and not self.cast_countdown_timer.isActive():
            print(f"开始CAST复制流程，队列中有 {len(self.cast_file_queue)} 个文件")
            self.cast_waiting_seconds = 0
            self.cast_countdown_label.setText("00:00")
            self.cast_countdown_timer.start(1000)
            self.cast_folder_check_timer.start(5000)
            self.check_cast_target_folder()

    def try_start_comac_copy_process(self):
        """尝试开始COMAC复制流程"""
        if (not self.is_running or self.is_paused or not self.comac_enabled or
            len(self.comac_file_queue) == 0):
            return

        if not self.comac_folder_check_timer.isActive() and not self.comac_countdown_timer.isActive():
            print(f"开始COMAC复制流程，队列中有 {len(self.comac_file_queue)} 个文件")
            self.comac_waiting_seconds = 0
            self.comac_countdown_label.setText("00:00")
            self.comac_countdown_timer.start(1000)
            self.comac_folder_check_timer.start(5000)
            self.check_comac_target_folder()

    def update_cast_countdown(self):
        """更新CAST等待时间显示"""
        if self.is_running and not self.is_paused and self.cast_enabled:
            self.cast_waiting_seconds += 1
            minutes = self.cast_waiting_seconds // 60
            seconds = self.cast_waiting_seconds % 60
            self.cast_countdown_label.setText(f"{minutes:02d}:{seconds:02d}")

    def update_comac_countdown(self):
        """更新COMAC等待时间显示"""
        if self.is_running and not self.is_paused and self.comac_enabled:
            self.comac_waiting_seconds += 1
            minutes = self.comac_waiting_seconds // 60
            seconds = self.comac_waiting_seconds % 60
            self.comac_countdown_label.setText(f"{minutes:02d}:{seconds:02d}")

    def check_cast_target_folder(self):
        """检查CAST目标文件夹是否为空"""
        if (not self.is_running or self.is_paused or not self.cast_enabled or
            len(self.cast_file_queue) == 0):
            self.cast_folder_check_timer.stop()
            self.cast_countdown_timer.stop()
            return

        try:
            if not os.path.exists(self.cast_target_path):
                os.makedirs(self.cast_target_path)
                print(f"创建CAST目标目录: {self.cast_target_path}")

            target_files = [f for f in os.listdir(self.cast_target_path)
                            if f.lower().endswith('.zip')]

            print(f"CAST目标文件夹检查：发现 {len(target_files)} 个ZIP文件")

            if not target_files:  # 如果目标文件夹为空
                # 停止检查定时器和倒计时
                self.cast_folder_check_timer.stop()
                self.cast_countdown_timer.stop()

                # 记录等待时间到总等待时间
                self.cast_total_waiting_seconds += self.cast_waiting_seconds

                # 5秒后执行复制
                print("CAST目标文件夹为空，5秒后开始复制")
                QTimer.singleShot(5000, self.copy_cast_files)
            else:
                print(f"CAST目标文件夹不为空，继续等待。文件: {target_files}")
            # 否则不做任何操作，继续等待下次检查

        except Exception as e:
            print(f"检查CAST目标文件夹出错: {str(e)}")

    def check_comac_target_folder(self):
        """检查COMAC目标文件夹是否为空"""
        if (not self.is_running or self.is_paused or not self.comac_enabled or
            len(self.comac_file_queue) == 0):
            self.comac_folder_check_timer.stop()
            self.comac_countdown_timer.stop()
            return

        try:
            if not os.path.exists(self.comac_target_path):
                os.makedirs(self.comac_target_path)
                print(f"创建COMAC目标目录: {self.comac_target_path}")

            target_files = [f for f in os.listdir(self.comac_target_path)
                            if f.lower().endswith('.zip')]

            print(f"COMAC目标文件夹检查：发现 {len(target_files)} 个ZIP文件")

            if not target_files:  # 如果目标文件夹为空
                # 停止检查定时器和倒计时
                self.comac_folder_check_timer.stop()
                self.comac_countdown_timer.stop()

                # 记录等待时间到总等待时间
                self.comac_total_waiting_seconds += self.comac_waiting_seconds

                # 5秒后执行复制
                print("COMAC目标文件夹为空，5秒后开始复制")
                QTimer.singleShot(5000, self.copy_comac_files)
            else:
                print(f"COMAC目标文件夹不为空，继续等待。文件: {target_files}")
            # 否则不做任何操作，继续等待下次检查

        except Exception as e:
            print(f"检查COMAC目标文件夹出错: {str(e)}")

    def can_copy_two_cast_files(self):
        """检查是否可以同时复制两个CAST文件（都小于10MB）"""
        if len(self.cast_file_queue) < 2:
            return False

        file1_size = self.cast_file_queue[0][2]  # 第一个文件大小
        file2_size = self.cast_file_queue[1][2]  # 第二个文件大小

        # 10MB = 10 * 1024 * 1024 bytes
        max_size = 10 * 1024 * 1024

        return file1_size < max_size and file2_size < max_size

    def copy_cast_files(self):
        """复制CAST文件"""
        if (len(self.cast_file_queue) == 0 or not self.is_running or
            self.is_paused or not self.cast_enabled):
            return

        copy_start_time = time.time()
        copied_dir = os.path.join(self.cast_source_path, "Copied")

        try:
            # 复制过程中检查状态
            if not self.is_running or self.is_paused or not self.cast_enabled:
                print("CAST复制过程中程序暂停")
                return

            if not os.path.exists(self.cast_target_path):
                os.makedirs(self.cast_target_path)

            if not os.path.exists(copied_dir):
                os.makedirs(copied_dir)

            # 决定复制一个还是两个文件
            if self.can_copy_two_cast_files():
                # 复制两个文件
                files_to_copy = [self.cast_file_queue.popleft(), self.cast_file_queue.popleft()]
                print("CAST同时复制两个小文件")
            else:
                # 复制一个文件
                files_to_copy = [self.cast_file_queue.popleft()]
                print("CAST复制一个文件")

            # 执行复制操作
            for file_path, file_name, file_size in files_to_copy:
                # 复制前再次检查状态
                if not self.is_running or self.is_paused or not self.cast_enabled:
                    print("CAST复制前程序暂停")
                    return

                if not os.path.exists(file_path):
                    print(f"CAST源文件不存在，跳过: {file_name}")
                    continue

                target_file = os.path.join(self.cast_target_path, file_name)

                print(f"开始复制CAST文件: {file_name} -> {self.cast_target_path}")
                shutil.copy2(file_path, target_file)
                print(f"CAST复制完成: {file_name}")

                copied_file_path = os.path.join(copied_dir, file_name)
                shutil.move(file_path, copied_file_path)
                print(f"移动到CAST Copied目录: {file_name}")

                self.cast_copied_count += 1

            copy_duration = time.time() - copy_start_time
            self.cast_copy_time += copy_duration

            # 重置当前等待时间
            self.cast_waiting_seconds = 0

            self.save_history_data()
            self.update_status_display()

            # 继续处理队列中的下一个文件（等待目标文件夹为空后）
            if len(self.cast_file_queue) > 0 and self.is_running and not self.is_paused and self.cast_enabled:
                print(f"CAST队列中还有 {len(self.cast_file_queue)} 个文件，等待目标文件夹为空后继续")
                # 不立即启动下一轮，而是等待目标文件夹为空

        except Exception as e:
            print(f"复制CAST文件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def can_copy_two_comac_files(self):
        """检查是否可以同时复制两个COMAC文件（都小于10MB）"""
        if len(self.comac_file_queue) < 2:
            return False

        file1_size = self.comac_file_queue[0][2]  # 第一个文件大小
        file2_size = self.comac_file_queue[1][2]  # 第二个文件大小

        # 10MB = 10 * 1024 * 1024 bytes
        max_size = 10 * 1024 * 1024

        return file1_size < max_size and file2_size < max_size

    def copy_comac_files(self):
        """复制COMAC文件"""
        if (len(self.comac_file_queue) == 0 or not self.is_running or
            self.is_paused or not self.comac_enabled):
            return

        copy_start_time = time.time()
        copied_dir = os.path.join(self.comac_source_path, "Copied")

        try:
            # 复制过程中检查状态
            if not self.is_running or self.is_paused or not self.comac_enabled:
                print("COMAC复制过程中程序暂停")
                return

            if not os.path.exists(self.comac_target_path):
                os.makedirs(self.comac_target_path)

            if not os.path.exists(copied_dir):
                os.makedirs(copied_dir)

            # 决定复制一个还是两个文件
            if self.can_copy_two_comac_files():
                # 复制两个文件
                files_to_copy = [self.comac_file_queue.popleft(), self.comac_file_queue.popleft()]
                print("COMAC同时复制两个小文件")
            else:
                # 复制一个文件
                files_to_copy = [self.comac_file_queue.popleft()]
                print("COMAC复制一个文件")

            # 执行复制操作
            for file_path, file_name, file_size in files_to_copy:
                # 复制前再次检查状态
                if not self.is_running or self.is_paused or not self.comac_enabled:
                    print("COMAC复制前程序暂停")
                    return

                if not os.path.exists(file_path):
                    print(f"COMAC源文件不存在，跳过: {file_name}")
                    continue

                target_file = os.path.join(self.comac_target_path, file_name)

                print(f"开始复制COMAC文件: {file_name} -> {self.comac_target_path}")
                shutil.copy2(file_path, target_file)
                print(f"COMAC复制完成: {file_name}")

                copied_file_path = os.path.join(copied_dir, file_name)
                shutil.move(file_path, copied_file_path)
                print(f"移动到COMAC Copied目录: {file_name}")

                self.comac_copied_count += 1

            copy_duration = time.time() - copy_start_time
            self.comac_copy_time += copy_duration

            # 重置当前等待时间
            self.comac_waiting_seconds = 0

            self.save_history_data()
            self.update_status_display()

            # 继续处理队列中的下一个文件（等待目标文件夹为空后）
            if len(self.comac_file_queue) > 0 and self.is_running and not self.is_paused and self.comac_enabled:
                print(f"COMAC队列中还有 {len(self.comac_file_queue)} 个文件，等待目标文件夹为空后继续")
                # 不立即启动下一轮，而是等待目标文件夹为空

        except Exception as e:
            print(f"复制COMAC文件时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def get_cast_remaining_files_count(self):
        """获取CAST剩余待复制文件数量"""
        if not os.path.exists(self.cast_source_path):
            return 0

        try:
            remaining_count = 0
            for f in os.listdir(self.cast_source_path):
                if f.lower().endswith('.zip') and not f.startswith('~$'):
                    remaining_count += 1
            return remaining_count
        except Exception as e:
            print(f"获取CAST剩余文件数出错: {str(e)}")
            return 0

    def get_comac_remaining_files_count(self):
        """获取COMAC剩余待复制文件数量"""
        if not os.path.exists(self.comac_source_path):
            return 0

        try:
            remaining_count = 0
            for f in os.listdir(self.comac_source_path):
                if f.lower().endswith('.zip') and not f.startswith('~$'):
                    remaining_count += 1
            return remaining_count
        except Exception as e:
            print(f"获取COMAC剩余文件数出错: {str(e)}")
            return 0

    def update_status_display(self):
        """更新状态显示"""
        # 更新ENC文件统计
        self.enc_stats_label.setText(f"ENC文件数: {self.enc_files_count}")

        # 更新CAST状态
        cast_remaining = self.get_cast_remaining_files_count()
        self.cast_stats_label.setText(f"已复制: {self.cast_copied_count} | 剩余: {cast_remaining}")

        # 更新COMAC状态
        comac_remaining = self.get_comac_remaining_files_count()
        self.comac_stats_label.setText(f"已复制: {self.comac_copied_count} | 剩余: {comac_remaining}")

        # 计算平均等待时间：（CAST总等待时间+COMAC总等待时间）/（CAST已复制数+COMAC已复制数）
        total_copied = self.cast_copied_count + self.comac_copied_count
        total_wait_time = self.cast_total_waiting_seconds + self.comac_total_waiting_seconds

        if total_copied > 0:
            average_wait_time = total_wait_time / total_copied
            avg_time_str = self.format_time_hhmm(average_wait_time)
        else:
            avg_time_str = "00:00"

        self.avg_wait_label.setText(avg_time_str)

    def update_runtime_display(self):
        """更新总运行时长显示"""
        if self.is_running and self.start_time:
            current_runtime = self.total_runtime + (time.time() - self.start_time.timestamp())
        else:
            current_runtime = self.total_runtime

        self.total_runtime_label.setText(self.format_time_duration(current_runtime))

    # 事件处理方法
    def on_source_directory_changed(self, path):
        """源文件夹变化事件处理"""
        print(f"检测到源文件夹变化: {path}")
        QTimer.singleShot(5000, self.scan_source_folder)

    def on_cast_directory_changed(self, path):
        """CAST文件夹变化事件处理"""
        print(f"检测到CAST文件夹变化: {path}")
        QTimer.singleShot(5000, self.scan_cast_source_folder)

    def on_comac_directory_changed(self, path):
        """COMAC文件夹变化事件处理"""
        print(f"检测到COMAC文件夹变化: {path}")
        QTimer.singleShot(5000, self.scan_comac_source_folder)

    def monitor_source_folder(self):
        """定时监控源文件夹"""
        self.scan_source_folder()
        self.scan_cast_source_folder()
        self.scan_comac_source_folder()

    def on_cast_checkbox_changed(self, state):
        """CAST复选框状态改变"""
        self.cast_enabled = state == Qt.Checked
        print(f"CAST复制开关: {'开启' if self.cast_enabled else '关闭'}")

    def on_comac_checkbox_changed(self, state):
        """COMAC复选框状态改变"""
        self.comac_enabled = state == Qt.Checked
        print(f"COMAC复制开关: {'开启' if self.comac_enabled else '关闭'}")

    # 路径选择方法
    def select_source_folder(self):
        """选择源文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择源文件夹", self.source_path)
        if folder:
            self.source_watcher.removePath(self.source_path)
            self.source_path = folder
            self.source_edit.setText(folder)
            self.source_watcher.addPath(self.source_path)
            self.scan_source_folder()
            self.update_status_display()

    def select_target_base_folder(self):
        """选择目标基础路径文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择目标路径文件夹", self.target_base_path)
        if folder:
            self.target_base_path = folder
            self.target_base_edit.setText(folder)

            # 更新子路径
            self.cast_target_path = os.path.join(self.target_base_path, "CAST")
            self.comac_target_path = os.path.join(self.target_base_path, "COMAC")

            # 确保目录存在
            self.ensure_directories_exist()

    # 控制按钮事件处理
    def start_monitoring_process(self):
        """开始监控流程"""
        # 确保目标路径存在
        try:
            self.ensure_directories_exist()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建必要目录: {str(e)}")
            return

        # 重置状态
        self.is_running = True
        self.is_paused = False
        self.start_time = datetime.now()

        # 更新按钮状态
        self.start_button.setEnabled(False)
        self.pause_button.setEnabled(True)

        print("开始自动监控模式")

        # 重新检测文件状态
        self.pending_folders.clear()
        self.pending_cast_files.clear()
        self.pending_comac_files.clear()

        # 重新启动所有定时器
        self.folder_monitor_timer.start(5000)
        self.folder_check_timer.start(500)
        self.cast_file_check_timer.start(500)
        self.comac_file_check_timer.start(500)

        # 重新扫描所有文件夹
        self.scan_source_folder()
        self.scan_cast_source_folder()
        self.scan_comac_source_folder()

        # 更新状态显示
        self.update_status_display()

        # 如果有文件在队列中，尝试开始相应的复制流程
        if len(self.folder_queue) > 0:
            QTimer.singleShot(1000, self.process_folder_queue)

        if len(self.cast_file_queue) > 0 and self.cast_enabled:
            QTimer.singleShot(1000, self.try_start_cast_copy_process)

        if len(self.comac_file_queue) > 0 and self.comac_enabled:
            QTimer.singleShot(1000, self.try_start_comac_copy_process)

    def pause_monitoring_process(self):
        """暂停监控流程"""
        if not self.is_running:
            return

        self.is_paused = True

        # 停止所有定时器
        self.folder_monitor_timer.stop()
        self.folder_check_timer.stop()
        self.cast_file_check_timer.stop()
        self.comac_file_check_timer.stop()
        self.cast_countdown_timer.stop()
        self.cast_folder_check_timer.stop()
        self.comac_countdown_timer.stop()
        self.comac_folder_check_timer.stop()

        # 更新按钮状态
        self.pause_button.setEnabled(False)
        self.start_button.setEnabled(True)

        # 保存数据
        self.save_history_data()

        print("监控已暂停")

    def exit_program(self):
        """退出程序"""
        try:
            print("正在退出程序...")

            # 设置退出标志
            self.is_running = False
            self.is_paused = True

            # 保存历史数据
            self.save_history_data()

            # 停止所有定时器
            timers = [
                self.folder_monitor_timer,
                self.folder_check_timer,
                self.cast_file_check_timer,
                self.comac_file_check_timer,
                self.cast_countdown_timer,
                self.cast_folder_check_timer,
                self.comac_countdown_timer,
                self.comac_folder_check_timer,
                self.runtime_update_timer
            ]

            for timer in timers:
                if timer.isActive():
                    timer.stop()

            # 关闭文件监控
            try:
                if self.source_watcher.directories():
                    self.source_watcher.removePath(self.source_path)
                if self.cast_watcher.directories():
                    self.cast_watcher.removePath(self.cast_source_path)
                if self.comac_watcher.directories():
                    self.comac_watcher.removePath(self.comac_source_path)
            except Exception as e:
                print(f"关闭文件监控时出错: {str(e)}")

            print("程序退出")

            # 强制退出应用程序
            QApplication.instance().quit()

        except Exception as e:
            print(f"退出程序时出错: {str(e)}")
            # 强制退出
            QApplication.instance().quit()

    def closeEvent(self, event):
        """程序关闭事件"""
        try:
            print("程序正在关闭...")

            # 设置退出标志
            self.is_running = False
            self.is_paused = True

            # 保存历史数据
            self.save_history_data()

            # 停止所有定时器
            timers = [
                self.folder_monitor_timer,
                self.folder_check_timer,
                self.cast_file_check_timer,
                self.comac_file_check_timer,
                self.cast_countdown_timer,
                self.cast_folder_check_timer,
                self.comac_countdown_timer,
                self.comac_folder_check_timer,
                self.runtime_update_timer
            ]

            for timer in timers:
                if timer.isActive():
                    timer.stop()

            # 关闭文件监控
            try:
                if self.source_watcher.directories():
                    self.source_watcher.removePath(self.source_path)
                if self.cast_watcher.directories():
                    self.cast_watcher.removePath(self.cast_source_path)
                if self.comac_watcher.directories():
                    self.comac_watcher.removePath(self.comac_source_path)
            except Exception as e:
                print(f"关闭文件监控时出错: {str(e)}")

            print("程序关闭完成")
            event.accept()

        except Exception as e:
            print(f"关闭程序时出错: {str(e)}")
            event.accept()


def setup_high_dpi_support():
    """设置高DPI支持和字体渲染优化"""
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)

    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    if hasattr(QApplication, 'setHighDpiScaleFactorRoundingPolicy'):
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    if hasattr(Qt, 'AA_UseDesktopOpenGL'):
        QApplication.setAttribute(Qt.AA_UseDesktopOpenGL, True)

    if hasattr(Qt, 'AA_UseSoftwareOpenGL'):
        QApplication.setAttribute(Qt.AA_UseSoftwareOpenGL, False)


if __name__ == "__main__":
    # 在创建QApplication之前设置高DPI支持
    setup_high_dpi_support()

    app = QApplication(sys.argv)

    # 设置默认字体
    font_db = QFontDatabase()
    default_font = QFont()
    default_font.setFamily("Microsoft YaHei UI")
    if not font_db.families().__contains__("Microsoft YaHei UI"):
        default_font.setFamily("SimHei")
    if not font_db.families().__contains__("SimHei"):
        default_font.setFamily("Arial Unicode MS")

    default_font.setHintingPreference(QFont.PreferFullHinting)
    default_font.setStyleStrategy(QFont.PreferAntialias)
    app.setFont(default_font)

    # 创建并显示窗口
    window = ENCAutoXferApp()
    window.show()

    # 居中显示窗口
    desktop = QDesktopWidget()
    screen_rect = desktop.screenGeometry()
    window_rect = window.geometry()
    x = (screen_rect.width() - window_rect.width()) // 2
    y = (screen_rect.height() - window_rect.height()) // 2
    window.move(x, y)

    sys.exit(app.exec_())
